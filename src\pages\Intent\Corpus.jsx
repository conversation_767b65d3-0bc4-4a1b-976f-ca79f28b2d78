import { useEffect, useState } from 'react'
import {
  Input,
  Space,
  Button,
  message,
  Table,
  Tooltip,
  Modal,
  Form,
  App,
  Switch,
  Badge,
  Tag
} from 'antd'
import { useParams, useNavigate } from 'react-router-dom'
import dayjs from 'dayjs'
import IconGoback from 'assets/svgs/goback.svg?react'
import ajax from '@/utils/http'
import IconAdd from '@/assets/svgs/add.svg?react'
import IconGeneralize from '@/assets/svgs/generalize.svg?react'
import {
  SearchOutlined,
  ArrowLeftOutlined,
  InfoCircleOutlined,
  EditOutlined,
  DeleteOutlined,
  ExclamationCircleOutlined
} from '@ant-design/icons'
import ExpandCorpusModal from './ExpandCorpusModal.jsx'
import { APP_ENV, cssVariables } from '@/utils/constant'
import styles from './style.module.scss'
import { MyModal } from '@/components'
import EntityDetail from './Entity/index.jsx'
import IconDelete from '@/assets/svgs/icon-delete.svg?react'
import IconDeleteRed from '@/assets/svgs/icon-delete-red.svg?react'

const Corpus = () => {
  const navigate = useNavigate()
  const { intentId } = useParams()
  const [searchCorpus, setSearchCorpus] = useState('')
  const [corpus, setCorpus] = useState('')
  const [loading, setLoading] = useState(false)
  const [corpusList, setCorpusList] = useState([])
  const [current, setCurrent] = useState(1)
  const [pageSize, setPageSize] = useState(10)
  const [total, setTotal] = useState(0)
  const [visible, setVisible] = useState(false)
  const [form] = Form.useForm()
  const [expandList, setExpandList] = useState([])
  const [expandModalVisible, setExpandModalVisible] = useState(false)
  const [expandLoading, setExpandLoading] = useState(false)

  const [publishModalVisible, setPublishModalVisible] = useState(false)
  const [publishForm] = Form.useForm()

  const [intentInfo, setIntentInfo] = useState(null)

  const [entityList, setEntityList] = useState([])
  const [searchEntity, setSearchEntity] = useState('')
  const [ePageData, setEPageData] = useState({ pageIndex: 1, pageSize: 10 })
  const [entityTotal, setEntityTotal] = useState(0)
  const [selectedRowKeys, setSelectedRowKeys] = useState([])
  const [entityCreateModalVisible, setEntityCreateModalVisible] = useState(false)
  const [entityForm] = Form.useForm()
  const [entityDetailVisible, setEntityDetailVisible] = useState(false)
  const [entitySelected, setEntitySelected] = useState(null)
  const [modal, contextHolder] = Modal.useModal()

  const columns = [
    {
      dataIndex: 'corpus',
      key: 'corpus',
      ellipsis: true
    },
    {
      fixed: 'right',
      width: 100,
      key: '',
      align: 'center',
      render: (_, record) => (
        <>
          {/* <Button
            type="primary"
            onClick={(e) => editCorpus(record, e)}
            key={`edit-${record.intentId}`}
          >
            编辑
          </Button> */}
          <div
            className={styles['delete-icon']}
            onClick={() => deleteCorpus(record.id)}
            key={`delete-${record.id}`}
          >
            <IconDelete className={styles['icon-normal']} />
            <IconDeleteRed className={styles['icon-hover']} />
          </div>
        </>
      )
    }
  ]
  const entityColumns = [
    {
      title: '实体名称',
      dataIndex: 'value',
      render: (text, record) => (
        <div>
          {text}&nbsp;
          <Tag color={record.type === 2 ? 'green' : 'cyan'}>
            {record.type === 2 ? '自定义' : '官方'}
          </Tag>
        </div>
      )
    },
    {
      title: '英文标识',
      dataIndex: 'name'
    },
    {
      title: '更新时间',
      dataIndex: 'updateTime',
      render: (text) => <div>{dayjs(text).format('YYYY-MM-DD HH:mm:ss')}</div>
    },
    {
      title: '被引用数',
      dataIndex: 'count',
      render: (text, record) => <div>{text || 0}</div>
    },
    {
      title: '状态',
      dataIndex: 'status',
      width: 110,
      render: (text) => (
        <Badge
          status={text === 1 ? 'success' : 'default'}
          text={text === 1 ? '已构建' : '未构建'}
        />
      )
    },
    {
      title: '操作',
      width: 120,
      render: (_, record) => (
        <Space>
          {record.type === 2 && (
            <Button type="link" icon={<EditOutlined />} onClick={() => {}}></Button>
          )}
          {record.type === 2 && (
            <Button
              type="link"
              danger
              icon={<DeleteOutlined />}
              onClick={(event) => {
                handleDelEntity(event, record)
              }}
            ></Button>
          )}
          {record.type === 1 && (
            <Switch
              checkedChildren="引用"
              unCheckedChildren="关闭"
              value={record.selected}
              onChange={(checked, e) => onSelect(record, checked)}
            />
          )}
        </Space>
      )
    }
  ]
  const handleDelEntity = (event, record) => {
    event.stopPropagation() // 阻止事件冒泡，避免触发行点击事件
    modal.confirm({
      title: '确定删除实体',
      icon: <ExclamationCircleOutlined />,
      content: '实体删除后不可恢复，请谨慎操作',
      okText: '确认',
      cancelText: '取消',
      onOk() {
        ajax({
          url: '/bot/entity/delete',
          data: {
            entityId: record.id
          },
          method: 'post',
          useFormData: true
        }).then((res) => {
          if (res.data.code === '0') {
            message.success('删除成功')
            getEntityList()
          }
        })
      }
    })
  }
  const onSelectChange = (newSelectedRowKeys) => {
    console.log('selectedRowKeys changed: ', newSelectedRowKeys)
    setSelectedRowKeys(newSelectedRowKeys)
  }

  const onSelect = async (record, selected, selectedRows) => {
    // console.log('onSelect', record, selected, selectedRows)
    if (record.status != 1 && record.type === 2) {
      setEntitySelected(record)
      setEntityDetailVisible(true)
      setTimeout(() => {
        setSelectedRowKeys(selectedRowKeys.filter((id) => id !== record.id))
      })
      return message.warning('当前实体未构建，请先构建实体')
    }
    let bindParams = {
      intentId,
      entityId: record.id,
      quoteFlag: selected
    }
    if (record.type === 2) {
      //自定义实体
      //先获取最新版本信息，再传参版本号
      let versionRes = await ajax({
        url: '/bot/entity/getEntityVersions',
        method: 'get',
        data: {
          entityId: record.id
        }
      })
      let versionObj = versionRes.data.data[0]
      bindParams['entityVersion'] = versionObj.number
    } else {
      //官方实体
      record.selected = selected
      setEntityList([...entityList])
      if (selected) {
        setSelectedRowKeys([...selectedRowKeys, record.id])
      } else {
        setSelectedRowKeys(selectedRowKeys.filter((id) => id !== record.id))
      }
    }
    ajax({
      url: '/aiui-agent/intent/bind/entity',
      method: 'post',
      data: bindParams
    }).then((res) => {
      if (res.data.code === '0') {
        message.success('操作成功')
      } else {
        getEntityList()
      }
    })
  }

  const rowSelection = {
    selectedRowKeys,
    hideSelectAll: true,
    onChange: onSelectChange,
    onSelect: onSelect
  }

  const handleClosePublishModal = () => {
    setPublishModalVisible(false)
    publishForm.resetFields()
  }

  const openExpandModal = () => {
    if (!corpus.trim()) {
      message.warning('语料内容不能为空')
      return
    }
    setExpandModalVisible(true)
    getExpandCorpusList()
  }

  const handleExpandModal = () => {
    setExpandModalVisible(false)
    setExpandList([])
  }

  const getExpandCorpusList = () => {
    setExpandLoading(true)
    const url = `/aiui-agent/tools/corpus/expand`
    const params = {
      corpus: corpus
      // intentId: intentId
    }
    ajax({
      url,
      data: params,
      method: 'post'
    })
      .then((res) => {
        if (res.data.code === '0') {
          setExpandLoading(false)
          const tableData = res.data.data.corpusList.map((item, index) => {
            return {
              key: index + 1,
              number: index + 1,
              content: item,
              added: false
            }
          })
          setExpandList(tableData)
        }
      })
      .catch((err) => {
        setExpandLoading(false)
      })
  }

  const updateExpandList = (key) => {
    setExpandList((prev) =>
      prev.map((item) => (item.key === key ? { ...item, added: true } : item))
    )
    getCorpusList()
  }

  const updateExpandAllList = () => {
    //  一次添加所有语料
    setExpandList((prev) =>
      prev.map((item) => {
        return {
          ...item,
          added: true
        }
      })
    )
    getCorpusList()
  }

  const deleteCorpus = (corpusId) => {
    const url = '/aiui-agent/intent/corpus/delete'
    const params = {
      intentId: intentId,
      corpusId: corpusId
    }
    ajax({
      url,
      data: params,
      method: 'post'
    })
      .then((res) => {
        if (res.data.code === '0') {
          message.success('操作成功')
          getCorpusList()
        }
      })
      .catch((err) => {})
  }

  const handelChange = (e) => {
    setCorpus(e.target.value)
  }

  const changeSearchInput = (e) => {
    setSearchCorpus(e.target.value)
  }

  const handlePressEnter = () => {
    if (!corpus.trim()) {
      message.warning('输入内容不能为空')
      return
    }
    addCorpus()
  }

  const addCorpus = () => {
    const url = `/aiui-agent/intent/corpus/add`
    const params = {
      intentId: intentId,
      corpusList: [corpus]
    }
    ajax({
      url,
      data: params,
      method: 'post'
    })
      .then((res) => {
        if (res.data.code === '0') {
          message.success('操作成功')
          getCorpusList()
          setCorpus('')
        }
      })
      .catch((err) => {})
  }

  const buildPublishIntent = () => {
    setLoading(true)
    const url = `/aiui-agent/intent/publish`
    const data = {
      intentId: intentId,
      remark: publishForm.getFieldValue('remark')
    }
    ajax({
      url,
      data,
      method: 'post'
    })
      .then((res) => {
        if (res.data.code === '0') {
          let bulidInfo = res.data.data
          message.success(
            `已发布成功,发布版本：${bulidInfo.version}， 包含语料数量：${bulidInfo.corpusCount}条`
          )
          setLoading(false)
          handleClosePublishModal()
        }
      })
      .catch((err) => {
        setLoading(false)
      })
  }

  const batchAddCorpus = () => {
    form
      .validateFields()
      .then((values) => {
        setLoading(false)
        const url = `/aiui-agent/intent/corpus/add`
        const params = {
          intentId: intentId,
          corpusList: values.batchCorpus.split('\n').filter((item) => item.trim() !== '')
        }
        ajax({
          url,
          data: params,
          method: 'post'
        })
          .then((res) => {
            if (res.data.code === '0') {
              message.success('添加成功')
              handleCloseModal()
              getCorpusList()
            }
          })
          .catch((err) => {})
      })
      .catch((err) => {})
  }

  const handleCloseModal = () => {
    setVisible(false)
    form.resetFields()
  }

  const handleModalShow = () => {
    setVisible(true)
  }

  const getCorpusList = () => {
    const url = `/aiui-agent/intent/corpus/list`
    const params = {
      corpus: searchCorpus,
      pageIndex: current,
      pageSize: pageSize,
      intentId: intentId
    }
    ajax({
      url,
      data: params,
      method: 'post'
    })
      .then((res) => {
        if (res.data.code === '0') {
          setCorpusList(res.data.data.data)
          setTotal(res.data.data.totalSize)
          const pageTotal = res.data.data.pageTotal
          if (current > pageTotal && pageTotal !== 0) {
            setCurrent(pageTotal) // 重置为最后一页
          }
        }
      })
      .catch((err) => {})
  }

  const changePagination = (paginationParams) => {
    setCurrent(paginationParams.current)
    setPageSize(paginationParams.pageSize)
  }

  const getIntentDetail = () => {
    ajax({
      url: '/aiui-agent/intent/detail',
      data: {
        intentId: intentId
      },
      method: 'post'
    })
      .then((res) => {
        if (res.data.code === '0') {
          setIntentInfo(res.data.data)
        }
      })
      .catch((err) => {})
  }

  const doBack = () => {
    if (APP_ENV === 'base') {
      navigate('/workspace/intent')
    } else if (APP_ENV === 'auto') {
      navigate('/intent')
    }
  }

  const getEntityList = () => {
    const url = `/bot/entity/getIntentEntityList`
    const params = {
      intentId: intentId,
      search: searchEntity,
      ...ePageData
    }
    ajax({
      url,
      data: params,
      method: 'get'
    })
      .then((res) => {
        if (res.data.code == '0') {
          // console.log('获取实体列表', res.data)
          if (res.data.data.results === 0 && ePageData.pageIndex > 1) {
            setEPageData({
              ...ePageData,
              pageIndex: ePageData.pageIndex - 1
            })
          }
          setEntityList(res.data.data.results)
          setEntityTotal(res.data.data.count)
          let results = res.data.data.results
          setSelectedRowKeys(results.filter((item) => item.selected).map((item) => item.id))
        }
      })
      .catch((err) => {})
  }
  const handleCreateEntity = () => {
    entityForm
      .validateFields()
      .then((values) => {
        // console.log('创建表单', values)
        const url = `/bot/entity/create`
        const params = {
          name: values.name,
          value: values.value
        }
        ajax({
          url,
          data: params,
          method: 'post',
          useFormData: true
        })
          .then((res) => {
            if (res.data.code === '0') {
              message.success('创建实体成功')
              setEntityCreateModalVisible(false)
              entityForm.resetFields()
              getEntityList()
            }
          })
          .catch((err) => {})
      })
      .catch((err) => {})
  }

  const handleCloseEntity = () => {
    setEntityCreateModalVisible(false)
    entityForm.resetFields()
  }

  useEffect(() => {
    getIntentDetail()
    getEntityList()
  }, [intentId])

  useEffect(() => {
    getCorpusList()
  }, [current, pageSize])

  useEffect(() => {
    getEntityList()
  }, [ePageData.pageIndex, ePageData.pageSize])

  return (
    <App>
      <div style={{ paddingBottom: '20px' }}>
        <div className={styles['intentHeader']}>
          <div style={{ display: 'flex', alignItems: 'center' }}>
            <div className={styles['return']} onClick={doBack}>
              <ArrowLeftOutlined />
            </div>
            我的意图
            <span className={styles['division']}>/</span>
            <span className={styles['intentName']}>{intentInfo?.intentName}</span>
          </div>
          <Button
            type="primary"
            loading={loading}
            onClick={() => {
              setPublishModalVisible(true)
            }}
          >
            构建发布意图
          </Button>
        </div>

        <div className={styles['exampleStatement']} style={{ marginBottom: '20px' }}>
          <div style={{ display: 'flex' }}>
            <div
              style={{
                width: '100px',
                height: '20px',
                alignItems: 'center',
                lineHeight: '20px',
                borderLeft: '2px solid blue',
                paddingLeft: '10px',
                marginTop: '10px'
              }}
            >
              示例说法
            </div>

            <Input
              value={searchCorpus}
              onChange={changeSearchInput}
              placeholder="搜索示例说法"
              style={{ width: '400px', marginLeft: 'auto', marginRight: '15px' }}
              suffix={<SearchOutlined onClick={getCorpusList} style={{ cursor: 'pointer' }} />}
              onPressEnter={getCorpusList}
            ></Input>
          </div>

          <div style={{ margin: '20px 0px' }}>
            <Input
              value={corpus}
              prefix={<IconAdd />}
              placeholder="回车添加用户的常用表达，例如：明天合肥天气怎么样"
              onChange={handelChange}
              onPressEnter={handlePressEnter}
              suffix={
                <Space>
                  <Tooltip title="泛化说法" placement="top">
                    <Button
                      type="text"
                      icon={<IconGeneralize />}
                      onClick={openExpandModal}
                    ></Button>
                  </Tooltip>
                  <Button type="text" onClick={handlePressEnter}>
                    添加
                  </Button>
                  <Button type="text" onClick={handleModalShow}>
                    批量添加
                  </Button>
                </Space>
              }
            >
              {/* <IconGeneralize /> */}
            </Input>
          </div>

          <Table
            showHeader={false}
            dataSource={corpusList}
            columns={columns}
            rowKey="id"
            bordered
            pagination={{
              position: ['bottomRight '],
              current,
              pageSize,
              total: total
            }}
            onChange={changePagination}
          ></Table>
        </div>

        <div className={styles['entity']}>
          <div style={{ display: 'flex', alignItems: 'center', marginBottom: '20px' }}>
            <div
              style={{
                width: '100px',
                height: '20px',
                alignItems: 'center',
                lineHeight: '20px',
                borderLeft: '2px solid blue',
                paddingLeft: '10px'
              }}
            >
              实体&nbsp;&nbsp;
              <Tooltip title="实体是指意图中需要提取的关键词，例如：地点、时间等">
                <InfoCircleOutlined />
              </Tooltip>
            </div>

            <Input
              value={searchEntity}
              onChange={(e) => setSearchEntity(e.target.value)}
              placeholder="搜索实体"
              style={{ width: '400px', marginLeft: 'auto', marginRight: '15px' }}
              suffix={<SearchOutlined onClick={getEntityList} style={{ cursor: 'pointer' }} />}
              onPressEnter={getEntityList}
            ></Input>

            <Button
              type="primary"
              onClick={() => {
                setEntityCreateModalVisible(true)
              }}
            >
              创建实体
            </Button>
          </div>
          <Table
            dataSource={entityList}
            columns={entityColumns}
            rowKey="id"
            rowSelection={rowSelection}
            pagination={{
              position: ['bottomRight '],
              current: ePageData.pageIndex,
              pageSize: ePageData.pageSize,
              total: entityTotal
            }}
            onChange={changePagination}
            onRow={(record) => {
              return {
                onClick: (event) => {
                  if (record.type === 1) {
                    return
                  }
                  setEntitySelected(record)
                  setEntityDetailVisible(true)
                } // 点击行
              }
            }}
            rowClassName={(record, index) => {
              if (record.type === 2) {
                return styles['click-row']
              }
            }}
          ></Table>
        </div>

        <MyModal
          open={visible}
          title="批量添加语料"
          onOk={batchAddCorpus}
          onClose={handleCloseModal}
          onCancel={handleCloseModal}
          destroyOnHidden={true}
          width={600}
          styles={{ footer: { textAlign: cssVariables[APP_ENV]['--modal-footer-text-align'] } }}
        >
          <Form form={form} layout="vertical">
            <Form.Item
              name="batchCorpus"
              label="语料"
              rules={[
                {
                  required: true,
                  trigger: 'blur'
                }
              ]}
            >
              <Input.TextArea
                placeholder="请输入语料,每行输入一句语料(最多不超过200行)"
                rows={8}
              ></Input.TextArea>
            </Form.Item>
          </Form>
        </MyModal>

        <ExpandCorpusModal
          expandModalVisible={expandModalVisible}
          handleExpandModal={handleExpandModal}
          expandList={expandList}
          expandLoading={expandLoading}
          updateExpandList={updateExpandList}
          updateExpandAllList={updateExpandAllList}
        ></ExpandCorpusModal>

        <MyModal
          title="发布说明"
          open={publishModalVisible}
          onOk={buildPublishIntent}
          onCancel={handleClosePublishModal}
          onClose={handleClosePublishModal}
        >
          <Form form={publishForm}>
            <Form.Item name="remark" rules={[{ required: false }]}>
              <Input.TextArea
                rows={6}
                placeholder="请填写发布说明，以便配置时候能够区分选择。"
              ></Input.TextArea>
            </Form.Item>
          </Form>
        </MyModal>

        <MyModal
          title="创建实体"
          open={entityCreateModalVisible}
          onOk={handleCreateEntity}
          onCancel={handleCloseEntity}
          cancelButtonProps={{ style: { display: 'inline-flex' } }}
        >
          <Form form={entityForm}>
            <Form.Item
              name="value"
              label="实体名称"
              rules={[
                {
                  required: true,
                  message: '请输入实体名称'
                },
                {
                  pattern: /^[\u4e00-\u9fa5a-zA-Z0-9._]{1,32}$/,
                  message: '仅支持中英文、数字、小数点、下划线，且不超过32个字符'
                }
              ]}
            >
              <Input placeholder="请输入实体名称"></Input>
            </Form.Item>
            <Form.Item
              name="name"
              label="英文标识"
              rules={[
                {
                  required: true,
                  message: '请输入英文标识'
                },
                {
                  pattern: /^[a-zA-Z0-9._]{1,32}$/,
                  message: '仅支持英文、数字、小数点、下划线，且不超过32个字符'
                }
              ]}
            >
              <Input placeholder="请输入英文标识"></Input>
            </Form.Item>
          </Form>
        </MyModal>

        {entityDetailVisible && (
          <EntityDetail
            entityDetailVisible={entityDetailVisible}
            setEntityDetailVisible={setEntityDetailVisible}
            entitySelected={entitySelected}
            refreshList={getEntityList}
          />
        )}
        {contextHolder}
      </div>
    </App>
  )
}

export default Corpus
